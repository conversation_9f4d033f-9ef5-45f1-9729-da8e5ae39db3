using InventoryManagement.DataAccess;
using InventoryManagement.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace InventoryManagement.Services
{
    /// <summary>
    /// Service implementation for transaction management operations
    /// </summary>
    public class TransactionService : ITransactionService
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly ILogger<TransactionService> _logger;

        public TransactionService(IUnitOfWork unitOfWork, ILogger<TransactionService> logger)
        {
            _unitOfWork = unitOfWork ?? throw new ArgumentNullException(nameof(unitOfWork));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        /// <summary>
        /// Gets all transactions
        /// </summary>
        public async Task<IEnumerable<Transaction>> GetAllTransactionsAsync()
        {
            try
            {
                return await _unitOfWork.Transactions.GetAll()
                    .OrderByDescending(t => t.TransactionDate)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting all transactions");
                throw;
            }
        }

        /// <summary>
        /// Gets a transaction by ID
        /// </summary>
        public async Task<Transaction> GetTransactionByIdAsync(int id)
        {
            try
            {
                return await _unitOfWork.Transactions.GetByIdAsync(id);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting transaction by ID: {TransactionId}", id);
                throw;
            }
        }

        /// <summary>
        /// Gets transactions by date range
        /// </summary>
        public async Task<IEnumerable<Transaction>> GetTransactionsByDateRangeAsync(DateTime startDate, DateTime endDate)
        {
            try
            {
                return await _unitOfWork.Transactions.GetByDateRangeAsync(startDate, endDate);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting transactions by date range: {StartDate} - {EndDate}", startDate, endDate);
                throw;
            }
        }

        /// <summary>
        /// Gets transactions by type
        /// </summary>
        public async Task<IEnumerable<Transaction>> GetTransactionsByTypeAsync(string transactionType)
        {
            try
            {
                if (Enum.TryParse<TransactionType>(transactionType, true, out var type))
                {
                    return await _unitOfWork.Transactions.GetByTypeAsync(type);
                }
                
                _logger.LogWarning("Invalid transaction type: {TransactionType}", transactionType);
                return new List<Transaction>();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting transactions by type: {TransactionType}", transactionType);
                throw;
            }
        }

        /// <summary>
        /// Creates a new transaction
        /// </summary>
        public async Task<Transaction> CreateTransactionAsync(Transaction transaction)
        {
            try
            {
                if (transaction == null)
                    throw new ArgumentNullException(nameof(transaction));

                // Validate transaction data
                ValidateTransaction(transaction);

                // Set creation date and generate reference number
                transaction.TransactionDate = DateTime.Now;
                transaction.ReferenceNumber = GenerateReferenceNumber();

                // Begin transaction
                _unitOfWork.BeginTransaction();

                try
                {
                    // Add the transaction
                    _unitOfWork.Transactions.Add(transaction);
                    await _unitOfWork.SaveChangesAsync();

                    // Update inventory if needed
                    await UpdateInventoryForTransactionAsync(transaction);

                    // Commit transaction
                    _unitOfWork.CommitTransaction();

                    _logger.LogInformation("Created new transaction: {ReferenceNumber} with total: {Total:C}", 
                        transaction.ReferenceNumber, transaction.Total);
                    
                    return transaction;
                }
                catch
                {
                    _unitOfWork.RollbackTransaction();
                    throw;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating transaction");
                throw;
            }
        }

        /// <summary>
        /// Updates an existing transaction
        /// </summary>
        public async Task<bool> UpdateTransactionAsync(Transaction transaction)
        {
            try
            {
                if (transaction == null)
                    throw new ArgumentNullException(nameof(transaction));

                // Validate transaction data
                ValidateTransaction(transaction);

                // Update the transaction
                _unitOfWork.Transactions.Update(transaction);
                await _unitOfWork.SaveChangesAsync();

                _logger.LogInformation("Updated transaction: {ReferenceNumber}", transaction.ReferenceNumber);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating transaction: {ReferenceNumber}", transaction?.ReferenceNumber);
                return false;
            }
        }

        /// <summary>
        /// Deletes a transaction
        /// </summary>
        public async Task<bool> DeleteTransactionAsync(int id)
        {
            try
            {
                var transaction = await _unitOfWork.Transactions.GetByIdAsync(id);
                if (transaction == null)
                {
                    _logger.LogWarning("Attempted to delete non-existent transaction with ID: {TransactionId}", id);
                    return false;
                }

                _unitOfWork.Transactions.Remove(transaction);
                await _unitOfWork.SaveChangesAsync();

                _logger.LogInformation("Deleted transaction: {ReferenceNumber}", transaction.ReferenceNumber);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting transaction with ID: {TransactionId}", id);
                return false;
            }
        }

        /// <summary>
        /// Gets transactions by user ID
        /// </summary>
        public async Task<IEnumerable<Transaction>> GetTransactionsByUserIdAsync(int userId)
        {
            try
            {
                return await _unitOfWork.Transactions.GetByUserIdAsync(userId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting transactions by user ID: {UserId}", userId);
                throw;
            }
        }

        /// <summary>
        /// Gets today's transactions
        /// </summary>
        public async Task<IEnumerable<Transaction>> GetTodaysTransactionsAsync()
        {
            try
            {
                return await _unitOfWork.Transactions.GetTodaysTransactionsAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting today's transactions");
                throw;
            }
        }

        /// <summary>
        /// Gets transaction by reference number
        /// </summary>
        public async Task<Transaction> GetTransactionByReferenceNumberAsync(string referenceNumber)
        {
            try
            {
                return await _unitOfWork.Transactions.GetByReferenceNumberAsync(referenceNumber);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting transaction by reference number: {ReferenceNumber}", referenceNumber);
                throw;
            }
        }

        /// <summary>
        /// Gets total sales amount for a date range
        /// </summary>
        public async Task<decimal> GetTotalSalesAmountAsync(DateTime startDate, DateTime endDate)
        {
            try
            {
                return await _unitOfWork.Transactions.GetTotalSalesAmountAsync(startDate, endDate);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting total sales amount for date range: {StartDate} - {EndDate}", startDate, endDate);
                throw;
            }
        }

        /// <summary>
        /// Validates transaction data
        /// </summary>
        private void ValidateTransaction(Transaction transaction)
        {
            if (transaction.Total < 0)
                throw new ArgumentException("Transaction total cannot be negative.");

            if (transaction.CashierId <= 0)
                throw new ArgumentException("Valid cashier ID is required.");

            if (transaction.Items == null || !transaction.Items.Any())
                throw new ArgumentException("Transaction must have at least one item.");
        }

        /// <summary>
        /// Generates a unique reference number for the transaction
        /// </summary>
        private string GenerateReferenceNumber()
        {
            return $"TXN-{DateTime.Now:yyyyMMdd}-{DateTime.Now.Ticks % 1000000:D6}";
        }

        /// <summary>
        /// Updates inventory based on transaction type
        /// </summary>
        private async Task UpdateInventoryForTransactionAsync(Transaction transaction)
        {
            // This would contain logic to update inventory based on transaction type
            // For now, we'll just log the action
            _logger.LogInformation("Inventory update triggered for transaction: {ReferenceNumber}", transaction.ReferenceNumber);
            
            // TODO: Implement inventory update logic based on transaction type
            await Task.CompletedTask;
        }
    }
}
