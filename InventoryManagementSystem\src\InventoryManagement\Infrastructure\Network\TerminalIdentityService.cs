using System;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using InventoryManagement.Infrastructure.Configuration;
using InventoryManagement.Infrastructure.Database;
using System.Text.Json;
using System.Net.NetworkInformation;
using System.Security.Cryptography;
using System.Text;
using InventoryManagement.DataAccess;
using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using InventoryManagement.Models;

namespace InventoryManagement.Infrastructure.Network
{
    public class TerminalIdentityService : ITerminalIdentityService
    {
        private readonly ILogger<TerminalIdentityService> _logger;
        private readonly ApplicationDbContext _dbContext;
        private readonly AppConfiguration _configuration;
        private readonly TerminalStorage _storage;
        private TerminalInfo _terminalInfo;
        private string _terminalId;
        private bool _isRegistered;

        public string TerminalId => _terminalId;
        public bool IsRegistered => _isRegistered;

        public TerminalIdentityService(
            ILogger<TerminalIdentityService> logger,
            ApplicationDbContext dbContext,
            AppConfiguration configuration)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _dbContext = dbContext ?? throw new ArgumentNullException(nameof(dbContext));
            _configuration = configuration ?? throw new ArgumentNullException(nameof(configuration));
            _storage = new TerminalStorage(logger);

            InitializeTerminalId();
        }

        private void InitializeTerminalId()
        {
            try
            {
                // Try to load existing terminal ID from storage
                var storedId = _storage.LoadTerminalId();
                if (!string.IsNullOrEmpty(storedId))
                {
                    _terminalId = storedId;
                    _logger.LogInformation("Loaded existing terminal ID: {TerminalId}", _terminalId);
                    return;
                }

                // Generate new terminal ID if none exists
                _terminalId = GenerateTerminalId();
                _storage.SaveTerminalId(_terminalId);
                _logger.LogInformation("Generated new terminal ID: {TerminalId}", _terminalId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to initialize terminal ID");
                throw new TerminalException("Failed to initialize terminal ID", ex);
            }
        }

        public async Task<bool> RegisterTerminalAsync(TerminalRegistrationInfo info)
        {
            try
            {
                _logger.LogInformation("Registering terminal {TerminalId}", _terminalId);

                // Create terminal info
                _terminalInfo = new TerminalInfo
                {
                    TerminalId = _terminalId,
                    MachineName = Environment.MachineName,
                    IpAddress = GetLocalIpAddress(),
                    OperatingSystem = Environment.OSVersion.ToString(),
                    ApplicationVersion = GetApplicationVersion(),
                    LastStartup = DateTime.UtcNow,
                    Status = new TerminalStatus
                    {
                        IsActive = true,
                        IsAuthorized = false,
                        LastSeen = DateTime.UtcNow,
                        HealthStatus = TerminalHealthStatus.Unknown
                    },
                    Capabilities = new Dictionary<string, string>()
                };

                // Store registration info
                var entity = new TerminalRegistrationEntity
                {
                    TerminalId = _terminalId,
                    LocationName = info.LocationName,
                    Department = info.Department,
                    ResponsiblePerson = info.ResponsiblePerson,
                    ContactInfo = info.ContactInfo,
                    CustomAttributes = JsonSerializer.Serialize(info.CustomAttributes),
                    RegisteredAt = DateTime.UtcNow,
                    LastUpdated = DateTime.UtcNow
                };

                _dbContext.TerminalRegistrations.Add(entity);
                await _dbContext.SaveChangesAsync();

                _isRegistered = true;
                _logger.LogInformation("Terminal {TerminalId} registered successfully", _terminalId);

                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to register terminal {TerminalId}", _terminalId);
                throw new TerminalException("Failed to register terminal", ex);
            }
        }

        public async Task<bool> ValidateTerminalAsync()
        {
            try
            {
                var registration = await _dbContext.TerminalRegistrations
                    .FirstOrDefaultAsync(t => t.TerminalId == _terminalId);

                if (registration == null)
                {
                    _logger.LogWarning("Terminal {TerminalId} not found in registration database", _terminalId);
                    _isRegistered = false;
                    return false;
                }

                _isRegistered = true;
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to validate terminal {TerminalId}", _terminalId);
                throw new TerminalException("Failed to validate terminal", ex);
            }
        }

        public async Task<TerminalStatus> GetStatusAsync()
        {
            if (_terminalInfo?.Status == null)
            {
                return new TerminalStatus
                {
                    IsActive = false,
                    IsAuthorized = false,
                    LastSeen = DateTime.MinValue,
                    HealthStatus = TerminalHealthStatus.Unknown
                };
            }

            return _terminalInfo.Status;
        }

        public async Task UpdateTerminalInfoAsync(TerminalInfo info)
        {
            try
            {
                _terminalInfo = info;
                var entity = await _dbContext.TerminalRegistrations
                    .FirstOrDefaultAsync(t => t.TerminalId == _terminalId);

                if (entity != null)
                {
                    entity.LastUpdated = DateTime.UtcNow;
                    entity.Status = JsonSerializer.Serialize(info.Status);
                    await _dbContext.SaveChangesAsync();
                }

                _logger.LogInformation("Updated terminal info for {TerminalId}", _terminalId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to update terminal info for {TerminalId}", _terminalId);
                throw new TerminalException("Failed to update terminal info", ex);
            }
        }

        public async Task<DateTime?> GetLastSyncTimeAsync()
        {
            try
            {
                var entity = await _dbContext.TerminalRegistrations
                    .FirstOrDefaultAsync(t => t.TerminalId == _terminalId);

                return entity?.LastSyncTime;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to get last sync time for {TerminalId}", _terminalId);
                throw new TerminalException("Failed to get last sync time", ex);
            }
        }

        public async Task SetLastSyncTimeAsync(DateTime syncTime)
        {
            try
            {
                var entity = await _dbContext.TerminalRegistrations
                    .FirstOrDefaultAsync(t => t.TerminalId == _terminalId);

                if (entity != null)
                {
                    entity.LastSyncTime = syncTime;
                    await _dbContext.SaveChangesAsync();
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to set last sync time for {TerminalId}", _terminalId);
                throw new TerminalException("Failed to set last sync time", ex);
            }
        }

        private string GenerateTerminalId()
        {
            // Generate a unique terminal ID based on hardware info
            var sb = new StringBuilder();
            
            // Add MAC address if available
            try
            {
                var firstMac = NetworkInterface.GetAllNetworkInterfaces()
                    .FirstOrDefault(ni => ni.OperationalStatus == OperationalStatus.Up)
                    ?.GetPhysicalAddress()
                    ?.ToString();

                if (!string.IsNullOrEmpty(firstMac))
                {
                    sb.Append(firstMac);
                }
            }
            catch
            {
                // Ignore network interface errors
            }

            // Add machine name
            sb.Append(Environment.MachineName);

            // Add timestamp
            sb.Append(DateTime.UtcNow.Ticks);

            // Hash the combined string
            using (var sha256 = SHA256.Create())
            {
                var hash = sha256.ComputeHash(Encoding.UTF8.GetBytes(sb.ToString()));
                return BitConverter.ToString(hash).Replace("-", "").Substring(0, 16);
            }
        }

        private string GetLocalIpAddress()
        {
            try
            {
                var firstInterface = NetworkInterface.GetAllNetworkInterfaces()
                    .FirstOrDefault(ni => 
                        ni.OperationalStatus == OperationalStatus.Up &&
                        ni.NetworkInterfaceType != NetworkInterfaceType.Loopback);

                if (firstInterface != null)
                {
                    var props = firstInterface.GetIPProperties();
                    var firstIpV4 = props.UnicastAddresses
                        .FirstOrDefault(addr => addr.Address.AddressFamily == System.Net.Sockets.AddressFamily.InterNetwork);

                    if (firstIpV4 != null)
                    {
                        return firstIpV4.Address.ToString();
                    }
                }
            }
            catch
            {
                // Ignore network interface errors
            }

            return "unknown";
        }

        private string GetApplicationVersion()
        {
            try
            {
                return System.Reflection.Assembly.GetExecutingAssembly().GetName().Version?.ToString() ?? "unknown";
            }
            catch
            {
                return "unknown";
            }
        }
    }


} 