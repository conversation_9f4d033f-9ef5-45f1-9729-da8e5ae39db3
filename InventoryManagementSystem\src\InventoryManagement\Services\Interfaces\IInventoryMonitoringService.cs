using InventoryManagement.Models;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace InventoryManagement.Services
{
    /// <summary>
    /// Service for monitoring inventory status and generating alerts
    /// </summary>
    public interface IInventoryMonitoringService
    {
        /// <summary>
        /// Get a summary of inventory status across all locations
        /// </summary>
        Task<InventoryStatusSummary> GetInventoryStatusSummaryAsync();
        
        /// <summary>
        /// Get items with inventory levels below their minimum threshold
        /// </summary>
        Task<List<LowStockItem>> GetLowStockItemsAsync();
        
        /// <summary>
        /// Get items that are close to expiration
        /// </summary>
        /// <param name="daysThreshold">Number of days to consider as "soon to expire"</param>
        Task<List<ExpiringItem>> GetSoonToExpireItemsAsync(int daysThreshold = 30);
        
        /// <summary>
        /// Get items that haven't moved (been sold) in a specified period
        /// </summary>
        /// <param name="daysThreshold">Number of days to consider as "inactive"</param>
        Task<List<InactiveItem>> GetInactiveItemsAsync(int daysThreshold = 90);
        
        /// <summary>
        /// Get a count of items requiring attention (low stock, expiring, etc.)
        /// </summary>
        Task<int> GetItemsRequiringAttentionCountAsync();
        
        /// <summary>
        /// Get stock levels by category
        /// </summary>
        Task<Dictionary<string, int>> GetStockLevelsByCategoryAsync();
        
        /// <summary>
        /// Get stock levels by location
        /// </summary>
        Task<Dictionary<string, int>> GetStockLevelsByLocationAsync();
        
        /// <summary>
        /// Set up monitoring thresholds for an item
        /// </summary>
        /// <param name="itemId">ID of the item</param>
        /// <param name="lowStockThreshold">Minimum stock threshold</param>
        /// <param name="expiryWarningDays">Days before expiry to start warning</param>
        /// <param name="inactivityThresholdDays">Days of inactivity to flag item</param>
        Task<bool> SetItemMonitoringThresholdsAsync(int itemId, int lowStockThreshold, int expiryWarningDays, int inactivityThresholdDays);
        
        /// <summary>
        /// Subscribe a user to receive alerts of specified types
        /// </summary>
        /// <param name="userId">ID of the user</param>
        /// <param name="alertTypes">Types of alerts to subscribe to</param>
        Task<bool> SubscribeToAlertsAsync(int userId, IEnumerable<string> alertTypes);
        
        /// <summary>
        /// Unsubscribe a user from receiving alerts of specified types
        /// </summary>
        /// <param name="userId">ID of the user</param>
        /// <param name="alertTypes">Types of alerts to unsubscribe from</param>
        Task<bool> UnsubscribeFromAlertsAsync(int userId, IEnumerable<string> alertTypes);
    }
    
    /// <summary>
    /// Summary of inventory status
    /// </summary>
    public class InventoryStatusSummary
    {
        /// <summary>
        /// Total number of unique items in inventory
        /// </summary>
        public int TotalItems { get; set; }
        
        /// <summary>
        /// Number of items with stock below minimum threshold
        /// </summary>
        public int LowStockItems { get; set; }
        
        /// <summary>
        /// Number of items close to expiration
        /// </summary>
        public int SoonToExpireItems { get; set; }
        
        /// <summary>
        /// Number of items that haven't been sold in a while
        /// </summary>
        public int InactiveItems { get; set; }
        
        /// <summary>
        /// Total value of current inventory at cost price
        /// </summary>
        public decimal InventoryValue { get; set; }
        
        /// <summary>
        /// When this summary was generated
        /// </summary>
        public DateTime LastUpdated { get; set; }
    }
    

    
    /// <summary>
    /// Represents an item that is expiring soon
    /// </summary>
    public class ExpiringItem
    {
        /// <summary>
        /// ID of the item
        /// </summary>
        public int ItemId { get; set; }
        
        /// <summary>
        /// Name of the item
        /// </summary>
        public string Name { get; set; }
        
        /// <summary>
        /// Item description
        /// </summary>
        public string Description { get; set; }
        
        /// <summary>
        /// Current stock level
        /// </summary>
        public int CurrentStock { get; set; }
        
        /// <summary>
        /// Item category
        /// </summary>
        public string Category { get; set; }
        
        /// <summary>
        /// Storage location
        /// </summary>
        public string Location { get; set; }
        
        /// <summary>
        /// When this item expires
        /// </summary>
        public DateTime ExpiryDate { get; set; }
        
        /// <summary>
        /// Days until expiry
        /// </summary>
        public int DaysUntilExpiry => (int)(ExpiryDate - DateTime.Now).TotalDays;
    }
    
    /// <summary>
    /// Represents an item that hasn't moved in a while
    /// </summary>
    public class InactiveItem
    {
        /// <summary>
        /// ID of the item
        /// </summary>
        public int ItemId { get; set; }
        
        /// <summary>
        /// Name of the item
        /// </summary>
        public string Name { get; set; }
        
        /// <summary>
        /// Current stock level
        /// </summary>
        public int CurrentStock { get; set; }
        
        /// <summary>
        /// Item category
        /// </summary>
        public string Category { get; set; }
        
        /// <summary>
        /// Storage location
        /// </summary>
        public string Location { get; set; }
        
        /// <summary>
        /// When the item was last sold
        /// </summary>
        public DateTime LastSoldDate { get; set; }
        
        /// <summary>
        /// Days since last sale
        /// </summary>
        public int DaysSinceLastSale => (int)(DateTime.Now - LastSoldDate).TotalDays;
    }
}
