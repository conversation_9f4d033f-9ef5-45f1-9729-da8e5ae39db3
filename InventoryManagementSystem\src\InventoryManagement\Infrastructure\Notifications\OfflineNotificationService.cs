using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using InventoryManagement.Services;
using InventoryManagement.Services.Interfaces;
using InventoryManagement.Events;
using InventoryManagement.Models;
using NotificationType = InventoryManagement.Models.NotificationType;

namespace InventoryManagement.Infrastructure.Notifications
{
    /// <summary>
    /// Unified notification service implementation for offline-only operation
    /// Replaces multiple overlapping notification services
    /// </summary>
    public class OfflineNotificationService : INotificationService
    {
        private readonly ILogger<OfflineNotificationService> _logger;
        private readonly List<NotificationInfo> _activeNotifications = new List<NotificationInfo>();
        private readonly Dictionary<Guid, Action> _notificationActions = new Dictionary<Guid, Action>();
        
        /// <summary>
        /// Event raised when a notification is shown
        /// </summary>
        public event EventHandler<NotificationEventArgs> NotificationShown;
        
        /// <summary>
        /// Event raised when a notification is dismissed
        /// </summary>
        public event EventHandler<NotificationEventArgs> NotificationDismissed;

        public OfflineNotificationService(ILogger<OfflineNotificationService> logger)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        /// <summary>
        /// Shows a notification to the user
        /// </summary>
        public async Task ShowNotificationAsync(string title, string message, NotificationType type = NotificationType.Information)
        {
            if (string.IsNullOrEmpty(title)) throw new ArgumentNullException(nameof(title));
            if (string.IsNullOrEmpty(message)) throw new ArgumentNullException(nameof(message));
            
            // Create notification info
            var notification = new NotificationInfo
            {
                NotificationId = Guid.NewGuid(),
                Title = title,
                Message = message,
                Type = type,
                Timestamp = DateTime.Now,
                HasAction = false
            };
            
            // Add to active notifications
            lock (_activeNotifications)
            {
                _activeNotifications.Add(notification);
            }
            
            // Log notification
            _logger.LogInformation("Showing notification: {Title} - {Message}", title, message);
            
            // Show notification in UI (dispatch to UI thread if needed)
            await ShowNotificationInUiAsync(notification);
            
            // Raise event
            OnNotificationShown(notification);
        }

        /// <summary>
        /// Shows a notification with a custom action
        /// </summary>
        public async Task ShowActionNotificationAsync(string title, string message, string actionText, Action action, NotificationType type = NotificationType.Information)
        {
            if (string.IsNullOrEmpty(title)) throw new ArgumentNullException(nameof(title));
            if (string.IsNullOrEmpty(message)) throw new ArgumentNullException(nameof(message));
            if (string.IsNullOrEmpty(actionText)) throw new ArgumentNullException(nameof(actionText));
            if (action == null) throw new ArgumentNullException(nameof(action));
            
            // Create notification info
            var notification = new NotificationInfo
            {
                NotificationId = Guid.NewGuid(),
                Title = title,
                Message = message,
                Type = type,
                Timestamp = DateTime.Now,
                HasAction = true,
                ActionText = actionText
            };
            
            // Add to active notifications and store action
            lock (_activeNotifications)
            {
                _activeNotifications.Add(notification);
                _notificationActions[notification.NotificationId] = action;
            }
            
            // Log notification
            _logger.LogInformation("Showing action notification: {Title} - {Message} - {ActionText}", 
                title, message, actionText);
            
            // Show notification in UI (dispatch to UI thread if needed)
            await ShowNotificationInUiAsync(notification);
            
            // Raise event
            OnNotificationShown(notification);
        }

        /// <summary>
        /// Gets all active notifications
        /// </summary>
        public List<NotificationInfo> GetActiveNotifications()
        {
            lock (_activeNotifications)
            {
                return _activeNotifications.ToList();
            }
        }

        /// <summary>
        /// Dismisses a notification by ID
        /// </summary>
        public bool DismissNotification(Guid notificationId)
        {
            NotificationInfo notification = null;
            
            lock (_activeNotifications)
            {
                notification = _activeNotifications.FirstOrDefault(n => n.NotificationId == notificationId);
                
                if (notification != null)
                {
                    _activeNotifications.Remove(notification);
                    _notificationActions.Remove(notificationId);
                }
            }
            
            if (notification != null)
            {
                _logger.LogInformation("Dismissed notification: {Title}", notification.Title);
                
                // Raise event
                OnNotificationDismissed(notification);
                
                return true;
            }
            
            return false;
        }

        /// <summary>
        /// Dismisses all active notifications
        /// </summary>
        public void DismissAllNotifications()
        {
            List<NotificationInfo> notifications;
            
            lock (_activeNotifications)
            {
                notifications = _activeNotifications.ToList();
                _activeNotifications.Clear();
                _notificationActions.Clear();
            }
            
            _logger.LogInformation("Dismissed all notifications ({Count})", notifications.Count);
            
            // Raise events for each dismissed notification
            foreach (var notification in notifications)
            {
                OnNotificationDismissed(notification);
            }
        }

        /// <summary>
        /// Executes the action for a notification
        /// </summary>
        public void ExecuteNotificationAction(Guid notificationId)
        {
            Action action = null;
            
            lock (_activeNotifications)
            {
                if (_notificationActions.TryGetValue(notificationId, out action))
                {
                    // Remove the notification after executing the action
                    DismissNotification(notificationId);
                }
            }
            
            if (action != null)
            {
                try
                {
                    action();
                    _logger.LogInformation("Executed action for notification: {NotificationId}", notificationId);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error executing action for notification: {NotificationId}", notificationId);
                }
            }
        }

        /// <summary>
        /// Shows a notification in the UI
        /// </summary>
        private async Task ShowNotificationInUiAsync(NotificationInfo notification)
        {
            // Ensure we're on the UI thread
            if (!Application.Current.Dispatcher.CheckAccess())
            {
                await Application.Current.Dispatcher.InvokeAsync(() => ShowNotificationInUi(notification));
            }
            else
            {
                ShowNotificationInUi(notification);
            }
        }

        /// <summary>
        /// Shows a notification in the UI (must be called on the UI thread)
        /// </summary>
        private void ShowNotificationInUi(NotificationInfo notification)
        {
            // Find the notification control in the main window
            // In a real implementation, this would show a notification in the UI
            // For simplicity, we'll just show a message box for now
            var messageBoxType = MessageBoxImage.Information;
            
            switch (notification.Type)
            {
                case NotificationType.Success:
                    messageBoxType = MessageBoxImage.Information;
                    break;
                case NotificationType.Warning:
                    messageBoxType = MessageBoxImage.Warning;
                    break;
                case NotificationType.Error:
                    messageBoxType = MessageBoxImage.Error;
                    break;
            }
            
            if (notification.HasAction)
            {
                var result = MessageBox.Show(
                    Application.Current.MainWindow,
                    notification.Message,
                    notification.Title,
                    MessageBoxButton.OKCancel,
                    messageBoxType);
                
                if (result == MessageBoxResult.OK)
                {
                    ExecuteNotificationAction(notification.NotificationId);
                }
                else
                {
                    DismissNotification(notification.NotificationId);
                }
            }
            else
            {
                MessageBox.Show(
                    Application.Current.MainWindow,
                    notification.Message,
                    notification.Title,
                    MessageBoxButton.OK,
                    messageBoxType);
                
                // Auto-dismiss the notification
                DismissNotification(notification.NotificationId);
            }
        }

        /// <summary>
        /// Raises the NotificationShown event
        /// </summary>
        protected virtual void OnNotificationShown(NotificationInfo notification)
        {
            NotificationShown?.Invoke(this, new NotificationEventArgs(notification));
        }

        /// <summary>
        /// Raises the NotificationDismissed event
        /// </summary>
        protected virtual void OnNotificationDismissed(NotificationInfo notification)
        {
            NotificationDismissed?.Invoke(this, new NotificationEventArgs(notification));
        }

        // INotificationService interface implementation
        public async Task SendNotificationAsync(string title, string message, NotificationPriority priority = NotificationPriority.Normal)
        {
            var notificationType = priority switch
            {
                NotificationPriority.Critical => NotificationType.Error,
                NotificationPriority.High => NotificationType.Warning,
                NotificationPriority.Low => NotificationType.Information,
                _ => NotificationType.Information
            };

            await ShowNotificationAsync(title, message, notificationType);
        }

        public async Task SendNotificationToUserAsync(int userId, string title, string message, NotificationPriority priority = NotificationPriority.Normal)
        {
            // In offline mode, we just show the notification regardless of user
            await SendNotificationAsync(title, message, priority);
        }

        public async Task SendNotificationToRoleAsync(string role, string title, string message, NotificationPriority priority = NotificationPriority.Normal)
        {
            // In offline mode, we just show the notification regardless of role
            await SendNotificationAsync(title, message, priority);
        }

        public async Task<IEnumerable<Models.Notification>> GetPendingNotificationsAsync(int userId)
        {
            // Convert our internal notifications to the expected format
            var notifications = GetActiveNotifications();
            return await Task.FromResult(notifications.Select(n => new Models.Notification
            {
                Id = n.NotificationId.GetHashCode(),
                UserId = userId,
                Title = n.Title,
                Message = n.Message,
                CreatedAt = n.Timestamp,
                IsRead = false,
                Priority = NotificationPriority.Normal
            }));
        }

        public async Task MarkNotificationAsReadAsync(int notificationId)
        {
            // Find notification by hash code and dismiss it
            var notification = GetActiveNotifications().FirstOrDefault(n => n.NotificationId.GetHashCode() == notificationId);
            if (notification != null)
            {
                DismissNotification(notification.NotificationId);
            }
            await Task.CompletedTask;
        }

        public async Task MarkAllNotificationsAsReadAsync(int userId)
        {
            DismissAllNotifications();
            await Task.CompletedTask;
        }

        public async Task<bool> SubscribeToAlertAsync(int userId, string alertType)
        {
            _logger.LogInformation("User {UserId} subscribed to alert type: {AlertType}", userId, alertType);
            // In offline mode, we just log the subscription
            return await Task.FromResult(true);
        }

        public async Task<bool> UnsubscribeFromAlertAsync(int userId, string alertType)
        {
            _logger.LogInformation("User {UserId} unsubscribed from alert type: {AlertType}", userId, alertType);
            // In offline mode, we just log the unsubscription
            return await Task.FromResult(true);
        }
    }
}
