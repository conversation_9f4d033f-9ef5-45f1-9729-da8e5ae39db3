using System;
using System.Collections.Generic;

namespace InventoryManagement.Models.Reports
{
    /// <summary>
    /// Base class for all reports with common properties
    /// </summary>
    public abstract class ReportBase
    {
        /// <summary>
        /// Unique identifier for the report
        /// </summary>
        public string ReportId { get; set; } = Guid.NewGuid().ToString();

        /// <summary>
        /// Title of the report
        /// </summary>
        public string Title { get; set; }

        /// <summary>
        /// When the report was generated
        /// </summary>
        public DateTime GeneratedAt { get; set; } = DateTime.Now;

        /// <summary>
        /// User who generated the report
        /// </summary>
        public string GeneratedBy { get; set; }

        /// <summary>
        /// Error message if report generation failed
        /// </summary>
        public string ErrorMessage { get; set; }
    }

    /// <summary>
    /// Comprehensive business performance report for administrators
    /// </summary>
    public class BusinessPerformanceReport : ReportBase
    {
        /// <summary>
        /// Start date of the report period
        /// </summary>
        public DateTime StartDate { get; set; }
        
        /// <summary>
        /// End date of the report period
        /// </summary>
        public DateTime EndDate { get; set; }
        
        /// <summary>
        /// Total revenue for the period
        /// </summary>
        public decimal TotalRevenue { get; set; }
        
        /// <summary>
        /// Total cost of goods sold
        /// </summary>
        public decimal TotalCOGS { get; set; }
        
        /// <summary>
        /// Gross profit (Revenue - COGS)
        /// </summary>
        public decimal GrossProfit { get; set; }
        
        /// <summary>
        /// Gross profit margin percentage
        /// </summary>
        public decimal GrossProfitMargin { get; set; }
        
        /// <summary>
        /// Total expenses for the period
        /// </summary>
        public decimal TotalExpenses { get; set; }
        
        /// <summary>
        /// Net profit (Gross Profit - Expenses)
        /// </summary>
        public decimal NetProfit { get; set; }
        
        /// <summary>
        /// Net profit margin percentage
        /// </summary>
        public decimal NetProfitMargin { get; set; }
        
        /// <summary>
        /// Total number of transactions
        /// </summary>
        public int TransactionCount { get; set; }
        
        /// <summary>
        /// Average transaction value
        /// </summary>
        public decimal AverageTransactionValue { get; set; }
        
        /// <summary>
        /// Daily sales breakdown
        /// </summary>
        public List<DailySalesSummary> DailySales { get; set; } = new List<DailySalesSummary>();
        
        /// <summary>
        /// Category performance breakdown
        /// </summary>
        public List<CategoryPerformance> CategoryPerformance { get; set; } = new List<CategoryPerformance>();
        
        /// <summary>
        /// Payment method breakdown
        /// </summary>
        public List<PaymentMethodSummary> PaymentMethodSummary { get; set; } = new List<PaymentMethodSummary>();
    }
    
    /// <summary>
    /// Daily sales summary for business performance report
    /// </summary>
    public class DailySalesSummary
    {
        /// <summary>
        /// Date of the sales summary
        /// </summary>
        public DateTime Date { get; set; }
        
        /// <summary>
        /// Total revenue for the day
        /// </summary>
        public decimal Revenue { get; set; }
        
        /// <summary>
        /// Total transactions for the day
        /// </summary>
        public int TransactionCount { get; set; }
        
        /// <summary>
        /// Total items sold for the day
        /// </summary>
        public int ItemsSold { get; set; }
    }
    
    /// <summary>
    /// Category performance for business report
    /// </summary>
    public class CategoryPerformance
    {
        /// <summary>
        /// Category ID
        /// </summary>
        public int CategoryId { get; set; }
        
        /// <summary>
        /// Category name
        /// </summary>
        public string CategoryName { get; set; }
        
        /// <summary>
        /// Total revenue for this category
        /// </summary>
        public decimal Revenue { get; set; }
        
        /// <summary>
        /// Revenue percentage of total
        /// </summary>
        public decimal RevenuePercentage { get; set; }
        
        /// <summary>
        /// Total cost for this category
        /// </summary>
        public decimal Cost { get; set; }
        
        /// <summary>
        /// Profit for this category
        /// </summary>
        public decimal Profit { get; set; }
        
        /// <summary>
        /// Profit margin percentage
        /// </summary>
        public decimal ProfitMargin { get; set; }
    }
    

    }
    
    /// <summary>
    /// Inventory valuation report for administrators
    /// </summary>
    public class InventoryValuationReport : ReportBase
    {
        /// <summary>
        /// As of date for the inventory valuation
        /// </summary>
        public DateTime AsOfDate { get; set; }
        
        /// <summary>
        /// Category ID filter if any
        /// </summary>
        public int? CategoryId { get; set; }
        
        /// <summary>
        /// Category name if filtered
        /// </summary>
        public string CategoryName { get; set; }
        
        /// <summary>
        /// Total inventory value
        /// </summary>
        public decimal TotalValue { get; set; }
        
        /// <summary>
        /// Total number of unique items
        /// </summary>
        public int TotalUniqueItems { get; set; }
        
        /// <summary>
        /// Total quantity of all items
        /// </summary>
        public int TotalQuantity { get; set; }
        
        /// <summary>
        /// Location breakdown of inventory
        /// </summary>
        public List<LocationInventoryValue> LocationValues { get; set; } = new List<LocationInventoryValue>();
        
        /// <summary>
        /// Category breakdown of inventory
        /// </summary>
        public List<CategoryInventoryValue> CategoryValues { get; set; } = new List<CategoryInventoryValue>();
    }
    
    /// <summary>
    /// Location inventory value for inventory valuation report
    /// </summary>
    public class LocationInventoryValue
    {
        /// <summary>
        /// Location ID
        /// </summary>
        public int LocationId { get; set; }
        
        /// <summary>
        /// Location name
        /// </summary>
        public string LocationName { get; set; }
        
        /// <summary>
        /// Total inventory value at this location
        /// </summary>
        public decimal Value { get; set; }
        
        /// <summary>
        /// Percentage of total inventory value
        /// </summary>
        public decimal Percentage { get; set; }
        
        /// <summary>
        /// Number of unique items at this location
        /// </summary>
        public int UniqueItems { get; set; }
        
        /// <summary>
        /// Total quantity of items at this location
        /// </summary>
        public int TotalQuantity { get; set; }
    }
    
    /// <summary>
    /// Category inventory value for inventory valuation report
    /// </summary>
    public class CategoryInventoryValue
    {
        /// <summary>
        /// Category ID
        /// </summary>
        public int CategoryId { get; set; }
        
        /// <summary>
        /// Category name
        /// </summary>
        public string CategoryName { get; set; }
        
        /// <summary>
        /// Total inventory value for this category
        /// </summary>
        public decimal Value { get; set; }
        
        /// <summary>
        /// Percentage of total inventory value
        /// </summary>
        public decimal Percentage { get; set; }
        
        /// <summary>
        /// Number of unique items in this category
        /// </summary>
        public int UniqueItems { get; set; }
        
        /// <summary>
        /// Total quantity of items in this category
        /// </summary>
        public int TotalQuantity { get; set; }
    }
    
    /// <summary>
    /// Audit trail report for administrators
    /// </summary>
    public class AuditTrailReport : ReportBase
    {
        /// <summary>
        /// Start date of the report period
        /// </summary>
        public DateTime StartDate { get; set; }
        
        /// <summary>
        /// End date of the report period
        /// </summary>
        public DateTime EndDate { get; set; }
        
        /// <summary>
        /// Entity type filter if any
        /// </summary>
        public string EntityType { get; set; }
        
        /// <summary>
        /// User ID filter if any
        /// </summary>
        public int? UserId { get; set; }
        
        /// <summary>
        /// List of audit trail entries
        /// </summary>
        public List<AuditTrailEntry> Entries { get; set; } = new List<AuditTrailEntry>();
    }
    
    /// <summary>
    /// Audit trail entry
    /// </summary>
    public class AuditTrailEntry
    {
        /// <summary>
        /// Audit log ID
        /// </summary>
        public int Id { get; set; }
        
        /// <summary>
        /// User ID who performed the action
        /// </summary>
        public int UserId { get; set; }
        
        /// <summary>
        /// User full name
        /// </summary>
        public string UserName { get; set; }
        
        /// <summary>
        /// Entity type that was modified
        /// </summary>
        public string EntityType { get; set; }
        
        /// <summary>
        /// Entity ID that was modified
        /// </summary>
        public string EntityId { get; set; }
        
        /// <summary>
        /// Action performed (Create, Update, Delete)
        /// </summary>
        public string Action { get; set; }
        
        /// <summary>
        /// Timestamp of the action
        /// </summary>
        public DateTime Timestamp { get; set; }
        
        /// <summary>
        /// Changes made in JSON format
        /// </summary>
        public string Changes { get; set; }
        
        /// <summary>
        /// IP address of the user
        /// </summary>
        public string IpAddress { get; set; }
    }
}
