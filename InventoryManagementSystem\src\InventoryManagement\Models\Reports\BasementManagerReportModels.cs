using System;
using System.Collections.Generic;

namespace InventoryManagement.Models.Reports
{
    /// <summary>
    /// Report of inventory transfers between basements
    /// </summary>
    public class BasementTransferReport : ReportBase
    {
        /// <summary>
        /// Start date of the report period
        /// </summary>
        public DateTime StartDate { get; set; }
        
        /// <summary>
        /// End date of the report period
        /// </summary>
        public DateTime EndDate { get; set; }
        
        /// <summary>
        /// Source location ID filter if any
        /// </summary>
        public int? SourceLocationId { get; set; }
        
        /// <summary>
        /// Source location name if filtered
        /// </summary>
        public string SourceLocationName { get; set; }
        
        /// <summary>
        /// Destination location ID filter if any
        /// </summary>
        public int? DestinationLocationId { get; set; }
        
        /// <summary>
        /// Destination location name if filtered
        /// </summary>
        public string DestinationLocationName { get; set; }
        
        /// <summary>
        /// Total number of transfers
        /// </summary>
        public int TotalTransfers { get; set; }
        
        /// <summary>
        /// Total number of items transferred
        /// </summary>
        public int TotalItemsTransferred { get; set; }
        
        /// <summary>
        /// Total value of items transferred
        /// </summary>
        public decimal TotalValueTransferred { get; set; }
        
        /// <summary>
        /// List of individual transfers
        /// </summary>
        public List<InventoryTransfer> Transfers { get; set; } = new List<InventoryTransfer>();
        
        /// <summary>
        /// Summary by location
        /// </summary>
        public List<LocationTransferSummary> LocationSummaries { get; set; } = new List<LocationTransferSummary>();
        
        /// <summary>
        /// Summary by category
        /// </summary>
        public List<CategoryTransferSummary> CategorySummaries { get; set; } = new List<CategoryTransferSummary>();
    }
    
    /// <summary>
    /// Individual inventory transfer
    /// </summary>
    public class InventoryTransfer
    {
        /// <summary>
        /// Transfer ID
        /// </summary>
        public int TransferId { get; set; }
        
        /// <summary>
        /// Date of the transfer
        /// </summary>
        public DateTime TransferDate { get; set; }
        
        /// <summary>
        /// Source location ID
        /// </summary>
        public int SourceLocationId { get; set; }
        
        /// <summary>
        /// Source location name
        /// </summary>
        public string SourceLocationName { get; set; }
        
        /// <summary>
        /// Destination location ID
        /// </summary>
        public int DestinationLocationId { get; set; }
        
        /// <summary>
        /// Destination location name
        /// </summary>
        public string DestinationLocationName { get; set; }
        
        /// <summary>
        /// Item ID
        /// </summary>
        public int ItemId { get; set; }
        
        /// <summary>
        /// Item name
        /// </summary>
        public string ItemName { get; set; }
        
        /// <summary>
        /// Category name
        /// </summary>
        public string CategoryName { get; set; }
        
        /// <summary>
        /// Quantity transferred
        /// </summary>
        public int Quantity { get; set; }
        
        /// <summary>
        /// Unit cost
        /// </summary>
        public decimal UnitCost { get; set; }
        
        /// <summary>
        /// Total value
        /// </summary>
        public decimal TotalValue { get; set; }
        
        /// <summary>
        /// User who initiated the transfer
        /// </summary>
        public string TransferredBy { get; set; }
        
        /// <summary>
        /// Notes or reason for the transfer
        /// </summary>
        public string Notes { get; set; }
    }
    
    /// <summary>
    /// Location transfer summary
    /// </summary>
    public class LocationTransferSummary
    {
        /// <summary>
        /// Location ID
        /// </summary>
        public int LocationId { get; set; }
        
        /// <summary>
        /// Location name
        /// </summary>
        public string LocationName { get; set; }
        
        /// <summary>
        /// Total items sent from this location
        /// </summary>
        public int ItemsSent { get; set; }
        
        /// <summary>
        /// Total value sent from this location
        /// </summary>
        public decimal ValueSent { get; set; }
        
        /// <summary>
        /// Total items received at this location
        /// </summary>
        public int ItemsReceived { get; set; }
        
        /// <summary>
        /// Total value received at this location
        /// </summary>
        public decimal ValueReceived { get; set; }
        
        /// <summary>
        /// Net change in items (Received - Sent)
        /// </summary>
        public int NetItemChange { get; set; }
        
        /// <summary>
        /// Net change in value (Received - Sent)
        /// </summary>
        public decimal NetValueChange { get; set; }
    }
    
    /// <summary>
    /// Category transfer summary
    /// </summary>
    public class CategoryTransferSummary
    {
        /// <summary>
        /// Category ID
        /// </summary>
        public int CategoryId { get; set; }
        
        /// <summary>
        /// Category name
        /// </summary>
        public string CategoryName { get; set; }
        
        /// <summary>
        /// Total items transferred in this category
        /// </summary>
        public int ItemsTransferred { get; set; }
        
        /// <summary>
        /// Total value transferred in this category
        /// </summary>
        public decimal ValueTransferred { get; set; }
        
        /// <summary>
        /// Percentage of total transfers
        /// </summary>
        public decimal Percentage { get; set; }
    }
    
    /// <summary>
    /// Basement inventory report
    /// </summary>
    public class BasementInventoryReport : ReportBase
    {
        /// <summary>
        /// Basement location ID
        /// </summary>
        public int BasementId { get; set; }
        
        /// <summary>
        /// Basement name
        /// </summary>
        public string BasementName { get; set; }
        
        /// <summary>
        /// As of date for the inventory
        /// </summary>
        public DateTime AsOfDate { get; set; }
        
        /// <summary>
        /// Category ID filter if any
        /// </summary>
        public int? CategoryId { get; set; }
        
        /// <summary>
        /// Category name if filtered
        /// </summary>
        public string CategoryName { get; set; }
        
        /// <summary>
        /// Total number of unique items
        /// </summary>
        public int TotalUniqueItems { get; set; }
        
        /// <summary>
        /// Total inventory quantity
        /// </summary>
        public int TotalQuantity { get; set; }
        
        /// <summary>
        /// Total inventory value
        /// </summary>
        public decimal TotalValue { get; set; }
        
        /// <summary>
        /// Number of low stock items
        /// </summary>
        public int LowStockItems { get; set; }
        
        /// <summary>
        /// Number of out of stock items
        /// </summary>
        public int OutOfStockItems { get; set; }
        
        /// <summary>
        /// Category breakdown
        /// </summary>
        public List<BasementCategorySummary> CategorySummaries { get; set; } = new List<BasementCategorySummary>();
        
        /// <summary>
        /// Item details
        /// </summary>
        public List<BasementInventoryItem> Items { get; set; } = new List<BasementInventoryItem>();
    }
    
    /// <summary>
    /// Basement category summary
    /// </summary>
    public class BasementCategorySummary
    {
        /// <summary>
        /// Category ID
        /// </summary>
        public int CategoryId { get; set; }
        
        /// <summary>
        /// Category name
        /// </summary>
        public string CategoryName { get; set; }
        
        /// <summary>
        /// Number of unique items in this category
        /// </summary>
        public int UniqueItems { get; set; }
        
        /// <summary>
        /// Total quantity of items in this category
        /// </summary>
        public int TotalQuantity { get; set; }
        
        /// <summary>
        /// Total value of items in this category
        /// </summary>
        public decimal TotalValue { get; set; }
        
        /// <summary>
        /// Percentage of total value
        /// </summary>
        public decimal ValuePercentage { get; set; }
    }
    
    /// <summary>
    /// Basement inventory item details
    /// </summary>
    public class BasementInventoryItem
    {
        /// <summary>
        /// Item ID
        /// </summary>
        public int ItemId { get; set; }
        
        /// <summary>
        /// Item name
        /// </summary>
        public string ItemName { get; set; }
        
        /// <summary>
        /// Category ID
        /// </summary>
        public int CategoryId { get; set; }
        
        /// <summary>
        /// Category name
        /// </summary>
        public string CategoryName { get; set; }
        
        /// <summary>
        /// Current quantity
        /// </summary>
        public int Quantity { get; set; }
        
        /// <summary>
        /// Unit cost
        /// </summary>
        public decimal UnitCost { get; set; }
        
        /// <summary>
        /// Total value
        /// </summary>
        public decimal TotalValue { get; set; }
        
        /// <summary>
        /// Minimum stock level
        /// </summary>
        public int MinimumStockLevel { get; set; }
        
        /// <summary>
        /// Whether the item is low on stock
        /// </summary>
        public bool IsLowStock { get; set; }
        
        /// <summary>
        /// Whether the item is out of stock
        /// </summary>
        public bool IsOutOfStock { get; set; }
        
        /// <summary>
        /// Last movement date
        /// </summary>
        public DateTime? LastMovementDate { get; set; }
    }
    
    /// <summary>
    /// Inventory aging report
    /// </summary>
    public class InventoryAgingReport : ReportBase
    {
        /// <summary>
        /// Basement ID
        /// </summary>
        public int BasementId { get; set; }
        
        /// <summary>
        /// Basement name
        /// </summary>
        public string BasementName { get; set; }
        
        /// <summary>
        /// Category ID filter if any
        /// </summary>
        public int? CategoryId { get; set; }
        
        /// <summary>
        /// Category name if filtered
        /// </summary>
        public string CategoryName { get; set; }
        
        /// <summary>
        /// As of date for the report
        /// </summary>
        public DateTime AsOfDate { get; set; }
        
        /// <summary>
        /// Total inventory value
        /// </summary>
        public decimal TotalValue { get; set; }
        
        /// <summary>
        /// Value of items by age bracket
        /// </summary>
        public List<AgingBracket> AgingBrackets { get; set; } = new List<AgingBracket>();
        
        /// <summary>
        /// Category breakdown
        /// </summary>
        public List<CategoryAgingSummary> CategorySummaries { get; set; } = new List<CategoryAgingSummary>();
        
        /// <summary>
        /// Items with aging details
        /// </summary>
        public List<ItemAgingDetail> AgingItems { get; set; } = new List<ItemAgingDetail>();
    }
    
    /// <summary>
    /// Aging bracket for inventory aging
    /// </summary>
    public class AgingBracket
    {
        /// <summary>
        /// Bracket name (e.g., "0-30 days")
        /// </summary>
        public string BracketName { get; set; }
        
        /// <summary>
        /// Minimum days in bracket
        /// </summary>
        public int MinDays { get; set; }
        
        /// <summary>
        /// Maximum days in bracket
        /// </summary>
        public int MaxDays { get; set; }
        
        /// <summary>
        /// Number of items in this bracket
        /// </summary>
        public int ItemCount { get; set; }
        
        /// <summary>
        /// Total quantity of items in this bracket
        /// </summary>
        public int Quantity { get; set; }
        
        /// <summary>
        /// Total value of items in this bracket
        /// </summary>
        public decimal Value { get; set; }
        
        /// <summary>
        /// Percentage of total value
        /// </summary>
        public decimal Percentage { get; set; }
    }
    
    /// <summary>
    /// Category aging summary
    /// </summary>
    public class CategoryAgingSummary
    {
        /// <summary>
        /// Category ID
        /// </summary>
        public int CategoryId { get; set; }
        
        /// <summary>
        /// Category name
        /// </summary>
        public string CategoryName { get; set; }
        
        /// <summary>
        /// Average age in days
        /// </summary>
        public int AverageAgeDays { get; set; }
        
        /// <summary>
        /// Value of items aged 0-30 days
        /// </summary>
        public decimal Value0To30Days { get; set; }
        
        /// <summary>
        /// Value of items aged 31-60 days
        /// </summary>
        public decimal Value31To60Days { get; set; }
        
        /// <summary>
        /// Value of items aged 61-90 days
        /// </summary>
        public decimal Value61To90Days { get; set; }
        
        /// <summary>
        /// Value of items aged over 90 days
        /// </summary>
        public decimal ValueOver90Days { get; set; }
        
        /// <summary>
        /// Total value
        /// </summary>
        public decimal TotalValue { get; set; }
    }
    
    /// <summary>
    /// Item aging detail
    /// </summary>
    public class ItemAgingDetail
    {
        /// <summary>
        /// Item ID
        /// </summary>
        public int ItemId { get; set; }
        
        /// <summary>
        /// Item name
        /// </summary>
        public string ItemName { get; set; }
        
        /// <summary>
        /// Category name
        /// </summary>
        public string CategoryName { get; set; }
        
        /// <summary>
        /// Current quantity
        /// </summary>
        public int Quantity { get; set; }
        
        /// <summary>
        /// Unit cost
        /// </summary>
        public decimal UnitCost { get; set; }
        
        /// <summary>
        /// Total value
        /// </summary>
        public decimal TotalValue { get; set; }
        
        /// <summary>
        /// Last movement date
        /// </summary>
        public DateTime? LastMovementDate { get; set; }
        
        /// <summary>
        /// Age in days
        /// </summary>
        public int AgeDays { get; set; }
        
        /// <summary>
        /// Age bracket
        /// </summary>
        public string AgeBracket { get; set; }
        
        /// <summary>
        /// Suggested action (e.g., "Promote", "Discount", "Write Off")
        /// </summary>
        public string SuggestedAction { get; set; }
    }
    
    /// <summary>
    /// Low stock report
    /// </summary>
    public class LowStockReport : ReportBase
    {
        /// <summary>
        /// Basement ID
        /// </summary>
        public int BasementId { get; set; }
        
        /// <summary>
        /// Basement name
        /// </summary>
        public string BasementName { get; set; }
        
        /// <summary>
        /// Category ID filter if any
        /// </summary>
        public int? CategoryId { get; set; }
        
        /// <summary>
        /// Category name if filtered
        /// </summary>
        public string CategoryName { get; set; }
        
        /// <summary>
        /// As of date for the report
        /// </summary>
        public DateTime AsOfDate { get; set; }
        
        /// <summary>
        /// Total low stock items
        /// </summary>
        public int TotalLowStockItems { get; set; }
        
        /// <summary>
        /// Total out of stock items
        /// </summary>
        public int TotalOutOfStockItems { get; set; }
        
        /// <summary>
        /// Category breakdown
        /// </summary>
        public List<CategoryLowStockSummary> CategorySummaries { get; set; } = new List<CategoryLowStockSummary>();
        
        /// <summary>
        /// Low stock items
        /// </summary>
        public List<LowStockItem> LowStockItems { get; set; } = new List<LowStockItem>();
    }
    
    /// <summary>
    /// Category low stock summary
    /// </summary>
    public class CategoryLowStockSummary
    {
        /// <summary>
        /// Category ID
        /// </summary>
        public int CategoryId { get; set; }
        
        /// <summary>
        /// Category name
        /// </summary>
        public string CategoryName { get; set; }
        
        /// <summary>
        /// Number of low stock items
        /// </summary>
        public int LowStockCount { get; set; }
        
        /// <summary>
        /// Number of out of stock items
        /// </summary>
        public int OutOfStockCount { get; set; }
        
        /// <summary>
        /// Total items in category
        /// </summary>
        public int TotalItems { get; set; }
        
        /// <summary>
        /// Percentage of items at low stock
        /// </summary>
        public decimal LowStockPercentage { get; set; }
    }

    /// <summary>
    /// Low stock item details
    /// </summary>
    public class LowStockItem
    {
        /// <summary>
        /// Item ID
        /// </summary>
        public int ItemId { get; set; }

        /// <summary>
        /// Item name
        /// </summary>
        public string ItemName { get; set; }

        /// <summary>
        /// Item SKU
        /// </summary>
        public string SKU { get; set; }

        /// <summary>
        /// Category name
        /// </summary>
        public string CategoryName { get; set; }

        /// <summary>
        /// Current quantity
        /// </summary>
        public int CurrentQuantity { get; set; }

        /// <summary>
        /// Minimum stock level
        /// </summary>
        public int MinimumStockLevel { get; set; }

        /// <summary>
        /// Reorder point
        /// </summary>
        public int ReorderPoint { get; set; }

        /// <summary>
        /// Whether the item is out of stock
        /// </summary>
        public bool IsOutOfStock { get; set; }

        /// <summary>
        /// Last movement date
        /// </summary>
        public DateTime? LastMovementDate { get; set; }

        /// <summary>
        /// Last reorder date
        /// </summary>
        public DateTime? LastReorderDate { get; set; }

        /// <summary>
        /// Average daily usage
        /// </summary>
        public decimal AverageDailyUsage { get; set; }

        /// <summary>
        /// Estimated days until stockout
        /// </summary>
        public int? EstimatedDaysUntilStockout { get; set; }
    }
}
