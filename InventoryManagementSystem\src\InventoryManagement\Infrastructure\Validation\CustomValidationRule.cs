using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Threading.Tasks;

namespace InventoryManagement.Infrastructure.Validation
{
    /// <summary>
    /// Represents a custom validation rule that can be defined by users and administrators
    /// </summary>
    public class CustomValidationRule
    {
        /// <summary>
        /// Unique identifier for the rule
        /// </summary>
        public Guid Id { get; set; } = Guid.NewGuid();
        
        /// <summary>
        /// Name of the rule
        /// </summary>
        public string Name { get; set; }
        
        /// <summary>
        /// Description of what the rule validates
        /// </summary>
        public string Description { get; set; }
        
        /// <summary>
        /// Entity type this rule applies to
        /// </summary>
        public string EntityType { get; set; }
        
        /// <summary>
        /// Property name this rule applies to (null if rule applies to entire entity)
        /// </summary>
        public string PropertyName { get; set; }
        
        /// <summary>
        /// Error message to display when validation fails
        /// </summary>
        public string ErrorMessage { get; set; }
        
        /// <summary>
        /// Severity of the validation failure
        /// </summary>
        public ValidationSeverity Severity { get; set; } = ValidationSeverity.Error;
        
        /// <summary>
        /// Whether this rule is currently active
        /// </summary>
        public bool IsActive { get; set; } = true;
        
        /// <summary>
        /// The validation expression stored as a string (for persistence)
        /// </summary>
        public string ValidationExpression { get; set; }
        
        /// <summary>
        /// User who created this rule
        /// </summary>
        public string CreatedBy { get; set; }
        
        /// <summary>
        /// When this rule was created
        /// </summary>
        public DateTime CreatedDate { get; set; } = DateTime.Now;
        
        /// <summary>
        /// Last user who modified this rule
        /// </summary>
        public string ModifiedBy { get; set; }
        
        /// <summary>
        /// When this rule was last modified
        /// </summary>
        public DateTime? ModifiedDate { get; set; }
        
        /// <summary>
        /// Creates a validation rule for a specific property and condition
        /// </summary>
        public static CustomValidationRule Create<TEntity, TProperty>(
            string name,
            Expression<Func<TEntity, TProperty>> propertyExpression,
            Expression<Func<TProperty, bool>> validationExpression,
            string errorMessage,
            ValidationSeverity severity = ValidationSeverity.Error)
        {
            var propertyInfo = GetPropertyInfo(propertyExpression);
            
            return new CustomValidationRule
            {
                Name = name,
                EntityType = typeof(TEntity).FullName,
                PropertyName = propertyInfo.Name,
                ErrorMessage = errorMessage,
                ValidationExpression = validationExpression.ToString(),
                Severity = severity
            };
        }
        
        /// <summary>
        /// Creates a validation rule for an entire entity
        /// </summary>
        public static CustomValidationRule CreateForEntity<TEntity>(
            string name,
            Expression<Func<TEntity, bool>> validationExpression,
            string errorMessage,
            ValidationSeverity severity = ValidationSeverity.Error)
        {
            return new CustomValidationRule
            {
                Name = name,
                EntityType = typeof(TEntity).FullName,
                PropertyName = null, // Applies to whole entity
                ErrorMessage = errorMessage,
                ValidationExpression = validationExpression.ToString(),
                Severity = severity
            };
        }
        
        private static System.Reflection.PropertyInfo GetPropertyInfo<TEntity, TProperty>(
            Expression<Func<TEntity, TProperty>> propertyExpression)
        {
            if (propertyExpression.Body is MemberExpression memberExpression)
            {
                return (System.Reflection.PropertyInfo)memberExpression.Member;
            }
            
            throw new ArgumentException("Expression is not a valid property expression", nameof(propertyExpression));
        }
    }
    

}
