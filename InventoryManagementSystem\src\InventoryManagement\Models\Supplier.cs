using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace InventoryManagement.Models
{
    /// <summary>
    /// Represents a supplier/vendor that provides items to the business
    /// </summary>
    public class Supplier
    {
        public int Id { get; set; }
        
        [Required(ErrorMessage = "Supplier name is required")]
        [MaxLength(100, ErrorMessage = "Name cannot exceed 100 characters")]
        public required string Name { get; set; }
        
        [MaxLength(200, ErrorMessage = "Contact name cannot exceed 200 characters")]
        public required string ContactPerson { get; set; }
        
        [Phone(ErrorMessage = "Invalid phone number format")]
        [MaxLength(20, ErrorMessage = "Phone number cannot exceed 20 characters")]
        public required string ContactPhone { get; set; }

        [EmailAddress(ErrorMessage = "Invalid email address format")]
        [MaxLength(100, ErrorMessage = "Email cannot exceed 100 characters")]
        public required string ContactEmail { get; set; }
        
        [MaxLength(200, ErrorMessage = "Address cannot exceed 200 characters")]
        public required string Address { get; set; }
        
        [MaxLength(50, ErrorMessage = "City cannot exceed 50 characters")]
        public required string City { get; set; }
        
        [MaxLength(50, ErrorMessage = "State/Province cannot exceed 50 characters")]
        public required string StateProvince { get; set; }
        
        [MaxLength(20, ErrorMessage = "Postal code cannot exceed 20 characters")]
        public required string PostalCode { get; set; }
        
        [MaxLength(50, ErrorMessage = "Country cannot exceed 50 characters")]
        public required string Country { get; set; }
        
        [MaxLength(50, ErrorMessage = "Tax ID cannot exceed 50 characters")]
        public required string TaxId { get; set; }
        
        [MaxLength(100, ErrorMessage = "Website cannot exceed 100 characters")]
        [Url(ErrorMessage = "Invalid website URL format")]
        public required string Website { get; set; }
        
        [MaxLength(500, ErrorMessage = "Notes cannot exceed 500 characters")]
        public required string Notes { get; set; }
        
        public decimal CreditLimit { get; set; }
        
        public decimal CurrentBalance { get; set; }
        
        public int PaymentTermDays { get; set; } // Number of days for payment terms
        
        [MaxLength(50, ErrorMessage = "Payment method cannot exceed 50 characters")]
        public required string PreferredPaymentMethod { get; set; }
        
        [MaxLength(50, ErrorMessage = "Currency cannot exceed 50 characters")]
        public required string PreferredCurrency { get; set; }

        [MaxLength(100, ErrorMessage = "Category cannot exceed 100 characters")]
        public required string Category { get; set; }

        public bool IsActive { get; set; }
        
        public DateTime CreatedDate { get; set; }
        
        public DateTime? LastModifiedDate { get; set; }
        
        public int? CreatedByUserId { get; set; }
        
        public int? LastModifiedByUserId { get; set; }
        
        // Soft delete implementation
        public bool IsDeleted { get; set; }
        
        public DateTime? DeletedDate { get; set; }
        
        public int? DeletedByUserId { get; set; }
        
        // Navigation properties
        public List<Item> SuppliedItems { get; set; } = new List<Item>();
        public List<DefectiveItem> DefectiveItems { get; set; } = new List<DefectiveItem>();
        public User CreatedByUser { get; set; }
        public User LastModifiedByUser { get; set; }
        public User DeletedByUser { get; set; }
    }
}
