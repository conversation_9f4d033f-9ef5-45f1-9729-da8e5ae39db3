using System;
using System.Collections.Generic;

namespace InventoryManagement.Models
{
    public enum ExchangeStatus
    {
        Initiated,
        ItemsTransferred,
        Completed,
        Cancelled
    }

    public class ItemExchange
    {
        public int Id { get; set; }
        public string ExchangeNumber { get; set; } // Unique identifier for the exchange
        public DateTime ExchangeDate { get; set; }
        public int CustomerId { get; set; } // Optional customer ID if tracked in system
        public string CustomerName { get; set; } // Customer name for reference
        public string CustomerContact { get; set; } // Phone number or other contact
        public string Reason { get; set; } // Reason for exchange
        public ExchangeStatus Status { get; set; }
        public int BasementManagerId { get; set; } // User who processed the item receipt
        public int? CashierId { get; set; } // User who processed the payment
        public decimal PriceDifference { get; set; } // Positive if customer pays, negative if refund
        public decimal TotalDifference { get; set; } // Total difference amount (calculated from exchange details)
        public bool IsPaid { get; set; } // Whether the price difference is settled
        public DateTime CreatedDate { get; set; }
        public int CreatedById { get; set; }
        public DateTime? CompletedDate { get; set; }
        
        // Navigation properties
        public User BasementManager { get; set; }
        public User Cashier { get; set; }
        public List<ItemExchangeDetail> ExchangeDetails { get; set; } = new List<ItemExchangeDetail>();
        public List<Payment> Payments { get; set; } = new List<Payment>();
    }

    // ItemExchangeDetail moved to its own file
}
